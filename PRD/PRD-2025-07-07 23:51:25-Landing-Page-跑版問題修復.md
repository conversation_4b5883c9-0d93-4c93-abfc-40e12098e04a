# PRD - Landing Page 跑版問題修復

**建立日期:** 2025-07-07 23:51:25  
**專案:** Android AutoLaunch Landing Page  
**問題類型:** 前端樣式修復  

## 問題描述

用戶回報 Landing Page 出現嚴重的跑版問題：
- 整個頁面樣式完全跑版
- 圖片無法正常顯示
- 版面配置錯亂

## 問題分析與診斷

### 使用 Playwright MCP 進行診斷

1. **瀏覽器檢視**
   - 透過 `mcp_playwright_browser_navigate` 檢視頁面
   - 使用 `mcp_playwright_browser_take_screenshot` 拍攝頁面截圖
   - 確認頁面確實存在嚴重的樣式問題

2. **主要問題發現**
   - Tailwind CSS 自訂顏色無法正確解析
   - `text-primary`、`bg-background`、`text-on-surface` 等自訂顏色導致樣式失效
   - 頁面顯示為裸露的 HTML 樣式（無 CSS 效果）

## 修復方案

### 1. 修正 Layout.astro
```astro
// 修正前
<body class="bg-background text-on-surface font-sans">

// 修正後  
<body class="bg-gray-50 text-gray-900 font-sans antialiased">
```

### 2. 替換自訂 Tailwind 顏色
將所有自訂顏色替換為標準 Tailwind 顏色：
- `text-primary` → `text-blue-600`
- `bg-background` → `bg-gray-50`
- `text-on-surface` → `text-gray-900`
- `hover:text-primary` → `hover:text-blue-600`

### 3. 重新建置和測試
```bash
npm run build
npm run dev
```

## 驗證結果

### 使用 Playwright 驗證修復效果

1. **視覺確認**
   - 重新截圖確認樣式正常載入
   - 漸層背景、圓角、陰影等效果正常顯示
   - 顏色配置符合設計預期

2. **功能測試**
   - FAQ 互動功能正常（點擊展開/摺疊）
   - 導航連結正常運作
   - 響應式設計正確

3. **建置驗證**
   - 最終建置成功
   - 靜態檔案正確產生在 `website/dist/`

## 技術細節

### 根本原因
自訂 Tailwind 顏色在開發環境中可能因為快取或編譯順序問題導致無法正確解析，使用標準 Tailwind 顏色可以確保樣式的穩定性。

### 修復的檔案
- `website/src/layouts/Layout.astro`
- `website/src/pages/index.astro`

### 工具使用
- **Playwright MCP**: 用於瀏覽器自動化測試和視覺確認
- **Astro.js**: 靜態網站產生器
- **Tailwind CSS**: 樣式框架

## 最終狀態

✅ **所有功能正常運作:**
- 現代化設計（漸層、卡片、陰影）
- 完整導航系統
- 英雄區塊展示
- 手機界面模擬
- 功能介紹卡片
- 互動式 FAQ
- 專業頁尾
- 響應式設計

## 後續建議

1. **使用標準顏色**: 避免過度依賴自訂 Tailwind 顏色，優先使用標準色彩
2. **定期測試**: 使用自動化工具定期檢查頁面狀態
3. **漸進式改進**: 如需自訂顏色，應在穩定後逐步引入並充分測試

---

**修復完成時間:** 2025-07-07 23:51:25  
**修復狀態:** ✅ 完全解決  
**負責人:** AI Assistant 