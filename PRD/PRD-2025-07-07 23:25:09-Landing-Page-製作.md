# PRD: Landing Page 製作

**日期**: 2025-07-07 23:25:09

**主題**: 為 Android AutoLaunch 專案建立一個新的登陸頁面 (Landing Page)

## 1. 需求背景

開發者希望為「Android AutoLaunch」應用程式建立一個專業的產品登陸頁面，以展示其核心功能、吸引潛在用戶，並提供下載管道。

## 2. 需求目標

- 建立一個資訊完整、設計現代化的登陸頁面。
- 頁面需包含專案介紹、核心功能、常見問題 (FAQ) 和下載連結。
- 網站技術棧需使用 Astro.js (最新版)、Vite 和 Tailwind CSS。
- 頁面排版風格參考 `https://gobag.tw/`。

## 3. 執行過程摘要

### 3.1. 資訊收集與分析

1.  **專案結構分析**: 首先，列出專案的整體檔案結構，以了解內容分佈。
2.  **文案提取**:
    - 閱讀並分析 `README.md`，提取出專案名稱、標語、核心功能描述、技術規格和常見問題等關鍵文案。
    - 根據分析結果，建立 `landing-page.md` 文件，集中管理所有頁面所需的文案、圖示路徑和配色方案。
3.  **視覺元素提取**:
    - **圖示**: 根據 `README.md` 的指引，在 `app/src/main/res/mipmap-xxxhdpi/` 目錄下找到了 `ic_launcher.webp` 作為網站 Logo。
    - **配色**: 查閱 `app/src/main/res/values/colors.xml`，提取出 Material Design 3 的淺色主題作為網站的主要配色方案。

### 3.2. 網站建置

1.  **專案初始化**:
    - 在根目錄下建立 `website` 資料夾。
    - 使用 `npm create astro@latest` 指令，搭配 `basics` 模板、`tailwind` 整合，成功初始化 Astro.js 專案。
    - 再次執行 `npm install` 以確保所有依賴都正確安裝。
2.  **資源準備**:
    - 將 `ic_launcher.webp` 圖示複製到 `website/public/` 目錄。
    - 修改 `tailwind.config.mjs`，將從 App 提取的配色方案設定為自訂主題色，以保持視覺一致性。
3.  **頁面開發**:
    - 清理 `src/pages/index.astro` 的預設內容。
    - **導覽列與英雄區塊**: 建立了一個響應式的導覽列和一個包含主標題、副標題及行動呼籲按鈕的英雄區塊。
    - **核心功能區塊**: 使用網格佈局展示三大核心功能，並為每個功能搭配了貼切的 Heroicons 圖示與描述。
    - **常見問題 (FAQ) 區塊**: 採用可折疊的 Accordion 樣式，並加入了 JavaScript 實現開合互動效果。
    - **頁尾**: 建立了一個包含版權資訊和相關連結的頁尾。
4.  **開發預覽**:
    - 啟動 Astro 的開發伺服器 (`npm run dev`)，並在開發過程中即時預覽頁面效果。

## 4. 最終交付成果

- 一個位於 `website/` 目錄下的 Astro.js 登陸頁面專案。
- 一個位於根目錄的 `landing-page.md`，整理了頁面的所有資源。
- 一份位於 `PRD/` 目錄下的本次開發過程記錄文件 (即本文件)。

## 5.遇到的問題與解決方案
- **Astro 初始化超時**: Astro CLI 在安裝依賴時顯示超時錯誤，但專案仍成功建立。透過在 `website` 目錄下再次執行 `npm install` 確保了依賴的完整性。
- **TypeScript Linter 錯誤**: 在 `tailwind.config.mjs` 中使用了 TypeScript 語法，導致 linter 報錯。透過移除 `satisfies` 關鍵字並改用 JSDoc 註解來解決。
- **JavaScript `null` 引用錯誤**: FAQ 區塊的互動腳本存在潛在的 `null` 引用風險。透過在存取 DOM 元素前加入 `if (element)` 檢查來修復。
- **Python `pytz` 模組缺失**: 產生 PRD 文件名時，執行 `get_time.py` 腳本因缺少 `pytz` 模組而失敗。透過 `pip3 install pytz` 安裝該模組後解決。
- **腳本執行路徑錯誤**: 多次因 `pwd` 不在預期的專案根目錄，導致檔案讀寫或腳本執行失敗。透過 `cd ..` 或提供完整路徑來修正。 