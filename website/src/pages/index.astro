---
import Layout from '../layouts/Layout.astro';

const navLinks = [
  { name: '核心功能', href: '#features' },
  { name: '常見問題', href: '#faq' },
  { name: '聯絡我們', href: 'mailto:<EMAIL>' },
];
---
<Layout title="Android AutoLaunch - 您的智慧應用啟動排程工具" description="一個 Android 應用程式，讓用戶可以在指定的時間和日期自動開啟其他應用程式。">
	<!-- Navigation -->
	<header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100">
		<div class="container mx-auto px-4">
			<div class="flex items-center justify-between h-16">
				<div class="flex items-center">
					<img src="/ic_launcher.webp" alt="Android AutoLaunch Logo" class="h-10 w-10 mr-3 rounded-lg shadow-sm">
					<span class="text-xl font-bold text-gray-900">Android AutoLaunch</span>
				</div>
				<nav class="hidden md:flex items-center space-x-8">
					{navLinks.map(link => 
						<a href={link.href} class="text-gray-600 hover:text-blue-600 transition-colors font-medium">
							{link.name}
						</a>
					)}
				</nav>
				<div class="md:hidden">
					<button class="text-gray-600">
						<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
						</svg>
					</button>
				</div>
			</div>
		</div>
	</header>

	<!-- Hero Section -->
	<section class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-16 pb-20">
		<div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23f0f0f0\' fill-opacity=\'0.3\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'1\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); opacity: 0.2;"></div>
		<div class="container mx-auto px-4 relative">
			<div class="text-center max-w-4xl mx-auto">
				<!-- App Icon -->
				<div class="inline-flex items-center justify-center w-20 h-20 bg-white rounded-3xl shadow-lg mb-8">
					<img src="/ic_launcher.webp" alt="Android AutoLaunch" class="w-16 h-16 rounded-2xl">
				</div>
				
				<!-- Main Heading -->
				<h1 class="text-5xl md:text-7xl font-extrabold text-gray-900 leading-tight mb-6">
					您的智慧應用啟動
					<span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
						排程工具
					</span>
				</h1>
				
				<!-- Subtitle -->
				<p class="text-xl md:text-2xl text-gray-600 mb-4 font-light leading-relaxed">
					根據您的時間需求，精確控制應用程式啟動時機
				</p>
				<p class="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
					支援應用程式和網頁排程，提供多語言介面，讓您在指定時間自動開啟所需的應用程式。
				</p>
				
				<!-- Download Buttons -->
				<div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
					<a href="#" class="inline-flex items-center bg-black text-white px-8 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
						<svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
							<path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
						</svg>
						Download on the App Store
					</a>
					<a href="#" class="inline-flex items-center bg-black text-white px-8 py-4 rounded-2xl hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
						<svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
							<path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
						</svg>
						Get it on Google Play
					</a>
				</div>
			</div>
		</div>
	</section>

	<!-- App Showcase Section -->
	<section class="py-20 bg-white">
		<div class="container mx-auto px-4">
			<div class="flex flex-col lg:flex-row items-center gap-16">
				<!-- Phone Mockup -->
				<div class="flex-1 flex justify-center">
					<div class="relative">
						<!-- Phone Frame -->
						<div class="relative w-80 h-[640px] bg-gray-900 rounded-[3rem] p-3 shadow-2xl">
							<div class="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
								<!-- Status Bar -->
								<div class="h-6 bg-gray-100 flex items-center justify-between px-6 text-xs font-semibold text-gray-800">
									<span>9:41</span>
									<div class="flex items-center space-x-1">
										<div class="w-4 h-2 bg-green-500 rounded-sm"></div>
										<div class="w-4 h-2 bg-gray-300 rounded-sm"></div>
									</div>
								</div>
								<!-- App Content Preview -->
								<div class="p-6 h-full bg-gradient-to-b from-blue-50 to-white">
									<h3 class="text-2xl font-bold text-gray-900 mb-4">我的排程</h3>
									<div class="space-y-4">
										<div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-3">
													<div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
														<svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
														</svg>
													</div>
													<div>
														<p class="font-semibold text-gray-900">晨間新聞</p>
														<p class="text-sm text-gray-500">每日 07:00</p>
													</div>
												</div>
												<div class="w-12 h-6 bg-green-500 rounded-full"></div>
											</div>
										</div>
										<div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
											<div class="flex items-center justify-between">
												<div class="flex items-center space-x-3">
													<div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
														<svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"></path>
														</svg>
													</div>
													<div>
														<p class="font-semibold text-gray-900">工作檢查清單</p>
														<p class="text-sm text-gray-500">週一至週五 09:00</p>
													</div>
												</div>
												<div class="w-12 h-6 bg-gray-300 rounded-full"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				
				<!-- Feature Highlights -->
				<div class="flex-1 space-y-8">
					<div>
						<h2 class="text-4xl font-bold text-gray-900 mb-6">
							讓您的手機
							<span class="text-blue-600">智慧啟動</span>
						</h2>
						<p class="text-xl text-gray-600 leading-relaxed">
							Android AutoLaunch 讓您完全掌控應用程式的啟動時機，無論是每日例行公事還是特定時間的提醒，都能精確執行。
						</p>
					</div>
					
					<div class="grid gap-6">
						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">精確排程</h3>
								<p class="text-gray-600">支援到分鐘級的精確時間控制，讓您的排程從不失誤。</p>
							</div>
						</div>
						
						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h5M20 20v-5h-5"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">靈活重複</h3>
								<p class="text-gray-600">單次、每日、每週或自訂重複模式，完全符合您的需求。</p>
							</div>
						</div>
						
						<div class="flex items-start space-x-4">
							<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
								<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"></path>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">雙重啟動</h3>
								<p class="text-gray-600">不僅能啟動應用程式，還能開啟指定網頁，一個工具雙重功能。</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section id="features" class="py-24 bg-gray-50">
		<div class="container mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">三大核心功能</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
					我們專注於應用排程的三大核心，幫助您為任何時間管理需求做好準備。
				</p>
			</div>
			
			<div class="grid md:grid-cols-3 gap-8">
				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">智能排程</h3>
						<p class="text-gray-600 leading-relaxed">
							不再煩惱何時啟動應用。根據您的作息時間，一鍵設定最適合的自動化排程方案。
						</p>
					</div>
				</div>

				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 15M20 20l-1.5-1.5A9 9 0 003.5 9" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">靈活重複</h3>
						<p class="text-gray-600 leading-relaxed">
							完整支援各種重複模式，整合精確的時間控制，一次設定即可永久離線使用。
						</p>
					</div>
				</div>

				<div class="text-center group">
					<div class="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
						<div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
							</svg>
						</div>
						<h3 class="text-2xl font-bold text-gray-900 mb-4">雙重啟動</h3>
						<p class="text-gray-600 leading-relaxed">
							App 不僅支援應用程式啟動，還能開啟網頁連結，確保您的數位生活永遠處於最佳狀態。
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section id="faq" class="py-24 bg-white">
		<div class="container mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">常見問題</h2>
				<p class="text-xl text-gray-600 max-w-2xl mx-auto">
					解答您可能有的疑問，讓您使用上更安心。
				</p>
			</div>
			
			<div class="max-w-4xl mx-auto">
				<div class="space-y-4">
					<div class="faq-item bg-gray-50 rounded-2xl overflow-hidden">
						<button class="faq-question flex items-center justify-between w-full text-left p-6 hover:bg-gray-100 transition-colors">
							<span class="text-lg font-semibold text-gray-900">這個 App 安全嗎？需要哪些權限？</span>
							<svg class="h-6 w-6 transform transition-transform text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer hidden px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">絕對安全。為了實現核心功能，App 需要 <code class="bg-gray-200 px-2 py-1 rounded text-sm">查詢已安裝應用</code>、<code class="bg-gray-200 px-2 py-1 rounded text-sm">開機後重新註冊排程</code>、和 <code class="bg-gray-200 px-2 py-1 rounded text-sm">設定精確鬧鐘</code> 等權限。所有權限都僅用於預定功能，我們絕不收集您的個人數據。</p>
						</div>
					</div>

					<div class="faq-item bg-gray-50 rounded-2xl overflow-hidden">
						<button class="faq-question flex items-center justify-between w-full text-left p-6 hover:bg-gray-100 transition-colors">
							<span class="text-lg font-semibold text-gray-900">在我的手機上，排程有時候不會準時執行？</span>
							<svg class="h-6 w-6 transform transition-transform text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer hidden px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">這通常是電池優化設定造成的。請到系統設定中，將「Android AutoLaunch」加入電池優化白名單，以確保系統不會為了省電而終止背景排程服務。我們的 App 已經針對各大廠商的省電機制進行優化。</p>
						</div>
					</div>

					<div class="faq-item bg-gray-50 rounded-2xl overflow-hidden">
						<button class="faq-question flex items-center justify-between w-full text-left p-6 hover:bg-gray-100 transition-colors">
							<span class="text-lg font-semibold text-gray-900">支援哪些語言？</span>
							<svg class="h-6 w-6 transform transition-transform text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer hidden px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">目前支援中文 (繁體)、English、日本語、한국어。我們會根據您的系統語言自動切換介面，您也可以在 App 內手動選擇偏好的語言。</p>
						</div>
					</div>

					<div class="faq-item bg-gray-50 rounded-2xl overflow-hidden">
						<button class="faq-question flex items-center justify-between w-full text-left p-6 hover:bg-gray-100 transition-colors">
							<span class="text-lg font-semibold text-gray-900">App 完全免費嗎？</span>
							<svg class="h-6 w-6 transform transition-transform text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
							</svg>
						</button>
						<div class="faq-answer hidden px-6 pb-6 text-gray-600">
							<p class="leading-relaxed">是的，「Android AutoLaunch」完全免費，且沒有任何廣告。我們相信好的工具應該讓每個人都能輕鬆使用，不會有隱藏費用或付費牆。</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<footer class="bg-gray-900 text-white">
		<div class="container mx-auto px-4 py-12">
			<div class="grid md:grid-cols-4 gap-8">
				<!-- Brand -->
				<div class="md:col-span-2">
					<div class="flex items-center mb-4">
						<img src="/ic_launcher.webp" alt="Android AutoLaunch Logo" class="h-10 w-10 mr-3 rounded-lg">
						<span class="text-xl font-bold">Android AutoLaunch</span>
					</div>
					<p class="text-gray-400 mb-6 max-w-md">
						讓您的 Android 裝置更加智慧化，透過精確的排程管理，提升您的數位生活品質。
					</p>
					<div class="flex space-x-4">
						<a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.09.111.104.209.077.32-.085.36-.276 1.122-.314 1.279-.049.2-.402.244-.402.244-.402-.244-.925-2.070-.925-3.33 0-3.771 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.750-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.90-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
							</svg>
						</a>
					</div>
				</div>
				
				<!-- Links -->
				<div>
					<h4 class="font-semibold mb-4">產品</h4>
					<ul class="space-y-2 text-gray-400">
						<li><a href="#features" class="hover:text-white transition-colors">功能介紹</a></li>
						<li><a href="#faq" class="hover:text-white transition-colors">常見問題</a></li>
						<li><a href="#" class="hover:text-white transition-colors">使用教學</a></li>
						<li><a href="#" class="hover:text-white transition-colors">版本更新</a></li>
					</ul>
				</div>
				
				<div>
					<h4 class="font-semibold mb-4">支援</h4>
					<ul class="space-y-2 text-gray-400">
						<li><a href="mailto:<EMAIL>" class="hover:text-white transition-colors">聯絡我們</a></li>
						<li><a href="#" class="hover:text-white transition-colors">隱私權政策</a></li>
						<li><a href="#" class="hover:text-white transition-colors">服務條款</a></li>
						<li><a href="#" class="hover:text-white transition-colors">GitHub</a></li>
					</ul>
				</div>
			</div>
			
			<div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
				<p class="text-gray-400 text-sm">&copy; 2025 Android AutoLaunch. All Rights Reserved.</p>
				<p class="text-gray-500 text-sm mt-2 md:mt-0">Made with ❤️ for Android users</p>
			</div>
		</div>
	</footer>

	<script>
		document.addEventListener('DOMContentLoaded', () => {
			const faqItems = document.querySelectorAll('.faq-item');
			faqItems.forEach(item => {
				const question = item.querySelector('.faq-question');
				const answer = item.querySelector('.faq-answer');
				const icon = item.querySelector('svg');

				if (question && answer && icon) {
					question.addEventListener('click', () => {
						const isVisible = !answer.classList.contains('hidden');
						answer.classList.toggle('hidden');
						icon.classList.toggle('rotate-180', !isVisible);
					});
				}
			});
		});
	</script>
</Layout>

