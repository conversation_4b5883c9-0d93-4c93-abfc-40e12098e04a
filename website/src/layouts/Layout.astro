--- 
import '../styles/tailwind.css';
interface Props { 
	title: string; 
	description: string; 
} 

const { title, description } = Astro.props; 
--- 

<!doctype html> 
<html lang="zh-TW"> 
	<head> 
		<meta charset="UTF-8" /> 
		<meta name="description" content={description} /> 
		<meta name="viewport" content="width=device-width" /> 
		<link rel="icon" type="image/webp" href="/ic_launcher.webp" /> 
		<meta name="generator" content={Astro.generator} /> 
		<title>{title}</title> 
	</head> 
	<body class="bg-gray-50 text-gray-900 font-sans antialiased"> 
		<slot /> 
	</body> 
</html>